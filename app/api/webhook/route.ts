import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { db } from '@/app/db'
import { purchaseOrders, userWorkspace, creditTransactions, webhookEvents, workspaces } from '@/app/db/schema'
import { eq, and, sql } from 'drizzle-orm'
import Strip<PERSON> from 'stripe'

// 产品配置映射
const PRODUCT_CONFIG = {
  'product_3': { credits: 1500000, amount: 300 },
  'product_10': { credits: 6000000, amount: 1000 },
  'product_100': { credits: 80000000, amount: 10000 },
} as const;

// 辅助函数：处理订单完成逻辑（从 verify-payment 复用）
async function processOrderCompletion(order: any, workspaceId: string, userId: string) {
  try {
    await db.transaction(async (tx) => {
      // 1. 更新订单状态
      await tx.update(purchaseOrders)
        .set({
          status: 'completed',
          completedAt: new Date()
        })
        .where(eq(purchaseOrders.id, order.id));

      // 2. 更新工作空间积分余额
      await tx.update(workspaces)
        .set({
          creditBalance: sql`${workspaces.creditBalance} + ${order.creditsPurchased}`,
          updatedAt: new Date()
        })
        .where(eq(workspaces.id, workspaceId));

      // 3. 获取更新后的工作空间积分余额
      const updatedWorkspace = await tx.query.workspaces.findFirst({
        where: eq(workspaces.id, workspaceId),
        columns: {
          creditBalance: true,
        }
      });

      if (!updatedWorkspace) {
        throw new Error('Workspace not found');
      }

      // 4. 创建积分交易记录
      await tx.insert(creditTransactions).values({
        workspaceId,
        userId,
        transactionType: 'purchase',
        inputCredits: 0,
        outputCredits: 0,
        totalCredits: order.creditsPurchased,
        balanceAfter: updatedWorkspace.creditBalance,
        relatedEntityId: order.id,
        relatedEntityType: 'PurchaseOrder',
        notes: `Purchase completed via webhook: ${order.packageType}, Amount: $${order.amountPaidCents / 100}`
      });
    });

    console.log(`Order ${order.id} completed successfully via webhook`);
    return { success: true };

  } catch (error) {
    console.error('Failed to complete order via webhook:', error);
    throw error;
  }
}

// 辅助函数：创建缺失的订单记录
async function createMissingOrder(session: Stripe.Checkout.Session) {
  const { workspaceId, userId, productId } = session.metadata || {};

  if (!workspaceId || !userId || !productId) {
    throw new Error('Missing required metadata in session');
  }

  const product = PRODUCT_CONFIG[productId as keyof typeof PRODUCT_CONFIG];
  if (!product) {
    throw new Error(`Invalid product configuration: ${productId}`);
  }

  // 创建订单记录
  const newOrder = await db.insert(purchaseOrders).values({
    workspaceId,
    userId,
    packageType: productId as 'product_3' | 'product_10' | 'product_100',
    stripeSessionId: session.id,
    status: 'pending',
    amountPaidCents: product.amount,
    currency: 'usd',
    creditsPurchased: product.credits,
  }).returning();

  return newOrder[0];
}

// 辅助函数：记录 webhook 事件
async function recordWebhookEvent(
  eventId: string,
  eventType: string,
  stripeSessionId?: string,
  orderId?: string,
  rawEventData?: any
) {
  await db.insert(webhookEvents).values({
    eventId,
    eventType,
    status: 'pending',
    stripeSessionId,
    orderId,
    rawEventData,
  });
}

// 辅助函数：更新 webhook 事件状态
async function updateWebhookEventStatus(
  eventId: string,
  status: 'processed' | 'failed',
  errorMessage?: string
) {
  await db.update(webhookEvents)
    .set({
      status,
      processedAt: status === 'processed' ? new Date() : undefined,
      errorMessage,
      updatedAt: new Date(),
    })
    .where(eq(webhookEvents.eventId, eventId));
}

// 辅助函数：检查事件是否已处理（幂等性）
async function isEventProcessed(eventId: string): Promise<boolean> {
  const existingEvent = await db.query.webhookEvents.findFirst({
    where: eq(webhookEvents.eventId, eventId)
  });

  return existingEvent?.status === 'processed';
}

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = headers().get('stripe-signature');

  if (!signature) {
    console.error('Missing stripe-signature header');
    return NextResponse.json(
      { error: 'Missing stripe-signature header' },
      { status: 400 }
    );
  }

  if (!process.env.STRIPE_WEBHOOK_SECRET) {
    console.error('Missing STRIPE_WEBHOOK_SECRET environment variable');
    return NextResponse.json(
      { error: 'Webhook secret not configured' },
      { status: 500 }
    );
  }

  let event: Stripe.Event;

  try {
    // 验证 webhook 签名
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET
    );
  } catch (err) {
    console.error('Webhook signature verification failed:', err);
    return NextResponse.json(
      { error: 'Invalid signature' },
      { status: 400 }
    );
  }

  console.log(`Received webhook event: ${event.type}, ID: ${event.id}`);

  try {
    // 检查事件是否已处理（幂等性）
    if (await isEventProcessed(event.id)) {
      console.log(`Event ${event.id} already processed, skipping`);
      return NextResponse.json({ received: true, status: 'already_processed' });
    }

    // 记录 webhook 事件
    await recordWebhookEvent(event.id, event.type, undefined, undefined, event.data);

    // 处理不同类型的事件
    switch (event.type) {
      case 'checkout.session.completed': {
        const session = event.data.object as Stripe.Checkout.Session;

        console.log(`Processing checkout.session.completed for session: ${session.id}`);

        // 验证支付状态
        if (session.payment_status !== 'paid') {
          console.log(`Session ${session.id} payment not completed, status: ${session.payment_status}`);
          await updateWebhookEventStatus(event.id, 'processed');
          return NextResponse.json({ received: true, status: 'payment_not_completed' });
        }

        // 查找对应的订单记录
        let existingOrder = await db.query.purchaseOrders.findFirst({
          where: eq(purchaseOrders.stripeSessionId, session.id)
        });

        // 如果订单不存在，尝试创建
        if (!existingOrder) {
          console.log(`Order not found for session ${session.id}, creating missing order`);
          try {
            existingOrder = await createMissingOrder(session);
          } catch (error) {
            console.error(`Failed to create missing order for session ${session.id}:`, error);
            await updateWebhookEventStatus(event.id, 'failed', `Failed to create order: ${error}`);
            return NextResponse.json(
              { error: 'Failed to create order record' },
              { status: 500 }
            );
          }
        }

        // 更新 webhook 事件记录，关联订单ID
        await db.update(webhookEvents)
          .set({
            stripeSessionId: session.id,
            orderId: existingOrder.id,
            updatedAt: new Date(),
          })
          .where(eq(webhookEvents.eventId, event.id));

        // 如果订单已经完成，跳过处理
        if (existingOrder.status === 'completed') {
          console.log(`Order ${existingOrder.id} already completed, skipping`);
          await updateWebhookEventStatus(event.id, 'processed');
          return NextResponse.json({ received: true, status: 'already_completed' });
        }

        // 验证会话元数据
        const { workspaceId, userId } = session.metadata || {};
        if (!workspaceId || !userId) {
          console.error(`Missing metadata in session ${session.id}`);
          await updateWebhookEventStatus(event.id, 'failed', 'Missing session metadata');
          return NextResponse.json(
            { error: 'Missing session metadata' },
            { status: 400 }
          );
        }

        // 处理订单完成
        try {
          await processOrderCompletion(existingOrder, workspaceId, userId);
          await updateWebhookEventStatus(event.id, 'processed');

          console.log(`Successfully processed order ${existingOrder.id} for user ${userId} in workspace ${workspaceId}`);

          return NextResponse.json({
            received: true,
            status: 'processed',
            orderId: existingOrder.id
          });
        } catch (error) {
          console.error(`Failed to process order completion for ${existingOrder.id}:`, error);
          await updateWebhookEventStatus(event.id, 'failed', `Order processing failed: ${error}`);
          return NextResponse.json(
            { error: 'Failed to process order completion' },
            { status: 500 }
          );
        }
      }

      case 'payment_intent.succeeded': {
        // 可以在这里处理其他支付成功事件
        console.log(`Payment intent succeeded: ${event.data.object.id}`);
        await updateWebhookEventStatus(event.id, 'processed');
        return NextResponse.json({ received: true, status: 'processed' });
      }

      case 'payment_intent.payment_failed': {
        // 处理支付失败事件
        console.log(`Payment intent failed: ${event.data.object.id}`);
        await updateWebhookEventStatus(event.id, 'processed');
        return NextResponse.json({ received: true, status: 'processed' });
      }

      default: {
        // 对于不处理的事件类型，标记为已处理
        console.log(`Unhandled event type: ${event.type}`);
        await updateWebhookEventStatus(event.id, 'processed');
        return NextResponse.json({ received: true, status: 'unhandled' });
      }
    }

  } catch (error) {
    console.error('Webhook processing error:', error);

    // 尝试更新事件状态为失败
    try {
      await updateWebhookEventStatus(event.id, 'failed', `Processing error: ${error}`);
    } catch (updateError) {
      console.error('Failed to update webhook event status:', updateError);
    }

    return NextResponse.json(
      { error: 'Webhook processing failed' },
      { status: 500 }
    );
  }
}