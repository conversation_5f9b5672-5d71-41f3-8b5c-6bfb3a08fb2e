import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { stripe } from '@/lib/stripe'
import { auth } from '@/auth'
import { db } from '@/app/db'
import { purchaseOrders } from '@/app/db/schema'
import { eq, and } from 'drizzle-orm'

export const runtime = 'nodejs';
// 产品配置映射
const PRODUCT_CONFIG = {
  'product_3': {
    priceId: process.env.STRIPE_PRICE_ID_3, // 替换为实际的 Stripe Price ID
    name: '1,500,000 积分',
    amount: 300, // $3.00 in cents
    credits: 1500000,
  },
  'product_10': {
    priceId: process.env.STRIPE_PRICE_ID_10, // 替换为实际的 Stripe Price ID
    name: '6,000,000 积分',
    amount: 1000, // $10.00 in cents
    credits: 6000000,
  },
  'product_100': {
    priceId: process.env.STRIPE_PRICE_ID_100, // 替换为实际的 Stripe Price ID
    name: '80,000,000 积分',
    amount: 10000, // $100.00 in cents
    credits: 80000000,
  },
} as const;

export async function POST(
  req: NextRequest,
  { params }: { params: Promise<{ workspace: string }> }
) {
  try {
    // 验证用户身份
    const session = await auth();
    if (!session?.user?.id || !session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 获取 workspaceId
    const { workspace: workspaceId } = await params;

    if (!workspaceId) {
      return NextResponse.json(
        { error: 'Workspace ID is required' },
        { status: 400 }
      );
    }

    // 获取请求体
    const body = await req.json();
    const { productId } = body;

    // 验证产品ID
    if (!productId || typeof productId !== 'string' || !(productId in PRODUCT_CONFIG)) {
      return NextResponse.json(
        { error: 'Invalid product ID' },
        { status: 400 }
      );
    }

    const product = PRODUCT_CONFIG[productId as keyof typeof PRODUCT_CONFIG];

    // 检查是否有未完成的订单（防止重复创建）
    const existingPendingOrder = await db.query.purchaseOrders.findFirst({
      where: and(
        eq(purchaseOrders.userId, session.user.id!),
        eq(purchaseOrders.workspaceId, workspaceId),
        eq(purchaseOrders.status, 'pending'),
        eq(purchaseOrders.packageType, productId as 'product_3' | 'product_10' | 'product_100')
      )
    });

    if (existingPendingOrder) {
      // 检查现有订单的创建时间，如果超过30分钟则允许创建新订单
      const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
      if (existingPendingOrder.createdAt && existingPendingOrder.createdAt > thirtyMinutesAgo) {
        return NextResponse.json(
          {
            error: 'You have a pending order for this product. Please use the "Continue Payment" option in the billing page or wait for the order to expire.',
            existingOrderId: existingPendingOrder.id,
            suggestContinuePayment: true
          },
          { status: 409 }
        );
      }
    }

    const headersList = headers();
    const origin = headersList.get('origin');

    if (!origin) {
      return NextResponse.json(
        { error: 'Origin header is required' },
        { status: 400 }
      );
    }

    // 创建 Stripe Checkout Session
    const checkoutSession = await stripe.checkout.sessions.create({
      line_items: [
        {
          price: product.priceId,
          quantity: 1,
        },
      ],
      mode: 'payment',
      success_url: `${origin}/${workspaceId}/admin/plans/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: `${origin}/${workspaceId}/admin/plans?canceled=true`,
      metadata: {
        workspaceId,
        userId: session.user.id,
        productId,
      },
      customer_email: session.user.email || undefined,
    });

    if (!checkoutSession.url) {
      return NextResponse.json(
        { error: 'Failed to create checkout session URL' },
        { status: 500 }
      );
    }

    // 创建订单记录
    try {
      await db.insert(purchaseOrders).values({
        workspaceId,
        userId: session.user.id!,
        packageType: productId as 'product_3' | 'product_10' | 'product_100',
        stripeSessionId: checkoutSession.id,
        status: 'pending',
        amountPaidCents: product.amount,
        currency: 'usd',
        creditsPurchased: product.credits,
      });
    } catch (error) {
      console.error('Failed to create purchase order:', error);
      // 即使订单记录创建失败，也返回支付链接，因为用户已经可以支付了
      // 后续可以通过webhook或其他方式补充订单记录
    }

    return NextResponse.json({ url: checkoutSession.url });
  } catch (err) {
    console.error('Checkout session creation error:', err);
    const error = err as Error & { statusCode?: number };
    return NextResponse.json(
      { error: error.message || 'An unexpected error occurred' },
      { status: error.statusCode || 500 }
    );
  }
}