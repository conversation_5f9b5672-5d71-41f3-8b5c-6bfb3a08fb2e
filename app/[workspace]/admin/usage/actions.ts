'use server';

import { db } from '@/app/db';
import { creditTransactions, users, userWorkspace, workspaces } from '@/app/db/schema';
import { eq, and, gte, lte, desc, count, like, sql } from 'drizzle-orm';
import { auth } from '@/auth';
import { isWorkspaceAdmin } from '@/app/utils/workspace';
import dayjs from 'dayjs';

export interface CreditTransactionFilter {
  startDate?: string;
  endDate?: string;
  userEmail?: string;
  page?: number;
  pageSize?: number;
}

export interface CreditTransactionRecord {
  id: string;
  createdAt: Date;
  userEmail: string;
  modelId: string;
  inputCredits: number;
  outputCredits: number;
  totalCredits: number;
  notes?: string;
}

export interface CreditAcquisitionRecord {
  id: string;
  createdAt: Date;
  totalCredits: number;
  notes?: string;
}

export interface CreditTransactionResponse {
  data: CreditTransactionRecord[];
  total: number;
  page: number;
  pageSize: number;
}

export interface CreditAcquisitionResponse {
  data: CreditAcquisitionRecord[];
  total: number;
  page: number;
  pageSize: number;
}

export interface DailyCreditConsumption {
  date: string;
  totalCredits: number;
}

export interface CreditConsumptionStatsResponse {
  data: DailyCreditConsumption[];
}

export interface UserCreditBalance {
  creditBalance: number;
  formattedBalance: string;
}

export interface UserCreditBalanceResponse {
  status: 'success' | 'error';
  data?: UserCreditBalance;
  message?: string;
}

/**
 * 获取积分消耗流水记录
 */
export async function getCreditTransactions(
  workspaceId: string,
  filter: CreditTransactionFilter = {}
): Promise<{ status: 'success' | 'error'; data?: CreditTransactionResponse; message?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { status: 'error', message: '未授权访问' };
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return { status: 'error', message: '权限不足，仅管理员可访问' };
    }

    const {
      startDate,
      endDate,
      userEmail,
      page = 1,
      pageSize = 10
    } = filter;

    // 构建查询条件
    const conditions = [
      eq(creditTransactions.workspaceId, workspaceId),
      eq(creditTransactions.transactionType, 'consumption') // 只查询消耗类型的记录
    ];

    // 日期筛选
    if (startDate) {
      conditions.push(gte(creditTransactions.createdAt, new Date(startDate)));
    }
    if (endDate) {
      conditions.push(lte(creditTransactions.createdAt, new Date(endDate + ' 23:59:59')));
    }

    // 用户邮箱筛选 - 需要关联用户表
    let userCondition = undefined;
    if (userEmail) {
      userCondition = like(users.email, `%${userEmail}%`);
    }

    // 计算总数
    const totalQuery = db
      .select({ count: count() })
      .from(creditTransactions)
      .leftJoin(users, eq(creditTransactions.userId, users.id))
      .where(and(...conditions, userCondition));

    const totalResult = await totalQuery;
    const total = totalResult[0]?.count || 0;

    // 查询数据
    const offset = (page - 1) * pageSize;
    const dataQuery = db
      .select({
        id: creditTransactions.id,
        createdAt: creditTransactions.createdAt,
        userEmail: users.email,
        modelId: creditTransactions.modelId,
        inputCredits: creditTransactions.inputCredits,
        outputCredits: creditTransactions.outputCredits,
        totalCredits: creditTransactions.totalCredits,
        notes: creditTransactions.notes,
      })
      .from(creditTransactions)
      .leftJoin(users, eq(creditTransactions.userId, users.id))
      .where(and(...conditions, userCondition))
      .orderBy(desc(creditTransactions.createdAt))
      .limit(pageSize)
      .offset(offset);

    const records = await dataQuery;

    const data: CreditTransactionRecord[] = records.map(record => ({
      id: record.id,
      createdAt: record.createdAt,
      userEmail: record.userEmail || '未知用户',
      modelId: record.modelId || '未知模型',
      inputCredits: Number(record.inputCredits) || 0,
      outputCredits: Number(record.outputCredits) || 0,
      totalCredits: Number(record.totalCredits) || 0,
      notes: record.notes || undefined,
    }));

    return {
      status: 'success',
      data: {
        data,
        total,
        page,
        pageSize
      }
    };

  } catch (error) {
    console.error('获取积分消耗流水失败:', error);
    return { status: 'error', message: '获取积分消耗流水失败' };
  }
}

/**
 * 获取最近30天的积分消耗统计
 */
export async function getCreditConsumptionStats(
  workspaceId: string
): Promise<{ status: 'success' | 'error'; data?: CreditConsumptionStatsResponse; message?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { status: 'error', message: '未授权访问' };
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return { status: 'error', message: '权限不足，仅管理员可访问' };
    }

    // 计算30天前的日期（包含今天）
    const endDate = dayjs();
    const startDate = endDate.subtract(29, 'day'); // 29天前 + 今天 = 30天

    // 生成完整的30天日期列表
    const dateList: string[] = [];
    for (let i = 0; i < 30; i++) {
      dateList.push(startDate.add(i, 'day').format('YYYY-MM-DD'));
    }

    // 查询积分消耗数据，按日期分组
    const consumptionData = await db
      .select({
        date: sql<string>`DATE(${creditTransactions.createdAt})`,
        totalCredits: sql<number>`SUM(ABS(${creditTransactions.totalCredits}))`
      })
      .from(creditTransactions)
      .where(
        and(
          eq(creditTransactions.workspaceId, workspaceId),
          eq(creditTransactions.transactionType, 'consumption'),
          gte(creditTransactions.createdAt, startDate.toDate()),
          lte(creditTransactions.createdAt, endDate.endOf('day').toDate())
        )
      )
      .groupBy(sql`DATE(${creditTransactions.createdAt})`)
      .orderBy(sql`DATE(${creditTransactions.createdAt})`);

    // 创建数据映射
    const dataMap = new Map<string, number>();
    consumptionData.forEach(item => {
      dataMap.set(item.date, Number(item.totalCredits) || 0);
    });

    // 生成完整的30天数据，确保所有日期都有数据
    const result: DailyCreditConsumption[] = dateList.map(date => ({
      date: dayjs(date).format('MM-DD'),
      totalCredits: dataMap.get(date) || 0
    }));

    return {
      status: 'success',
      data: {
        data: result
      }
    };

  } catch (error) {
    console.error('获取积分消耗统计失败:', error);
    return { status: 'error', message: '获取积分消耗统计失败' };
  }
}

/**
 * 获取工作空间的积分余额
 */
export async function getUserCreditBalance(
  workspaceId: string
): Promise<UserCreditBalanceResponse> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { status: 'error', message: '未授权访问' };
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return { status: 'error', message: '权限不足，仅管理员可访问' };
    }

    // 查询工作空间的积分余额
    const workspaceResult = await db.query.workspaces.findFirst({
      where: eq(workspaces.id, workspaceId),
      columns: {
        creditBalance: true,
      }
    });

    if (!workspaceResult) {
      return { status: 'error', message: '工作空间不存在' };
    }

    const creditBalance = Number(workspaceResult.creditBalance) || 0;
    const formattedBalance = creditBalance.toLocaleString();

    return {
      status: 'success',
      data: {
        creditBalance,
        formattedBalance
      }
    };

  } catch (error) {
    console.error('获取工作空间积分余额失败:', error);
    return { status: 'error', message: '获取工作空间积分余额失败' };
  }
}

/**
 * 获取积分获取流水记录
 */
export async function getCreditAcquisitions(
  workspaceId: string,
  filter: CreditTransactionFilter = {}
): Promise<{ status: 'success' | 'error'; data?: CreditAcquisitionResponse; message?: string }> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { status: 'error', message: '未授权访问' };
    }

    // 验证用户是否为工作空间管理员
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return { status: 'error', message: '权限不足，仅管理员可访问' };
    }

    const {
      startDate,
      endDate,
      page = 1,
      pageSize = 10
    } = filter;

    // 构建查询条件 - 查询积分获取类型的记录
    const conditions = [
      eq(creditTransactions.workspaceId, workspaceId),
      // 查询 purchase、refund、gift、redemption_code 类型的记录（积分获取）
      sql`${creditTransactions.transactionType} IN ('purchase', 'refund', 'gift', 'redemption_code')`
    ];

    // 日期筛选
    if (startDate) {
      conditions.push(gte(creditTransactions.createdAt, new Date(startDate)));
    }
    if (endDate) {
      conditions.push(lte(creditTransactions.createdAt, new Date(endDate + ' 23:59:59')));
    }

    // 计算总数
    const totalQuery = db
      .select({ count: count() })
      .from(creditTransactions)
      .where(and(...conditions));

    const totalResult = await totalQuery;
    const total = totalResult[0]?.count || 0;

    // 查询数据
    const offset = (page - 1) * pageSize;
    const dataQuery = db
      .select({
        id: creditTransactions.id,
        createdAt: creditTransactions.createdAt,
        totalCredits: creditTransactions.totalCredits,
        notes: creditTransactions.notes,
      })
      .from(creditTransactions)
      .where(and(...conditions))
      .orderBy(desc(creditTransactions.createdAt))
      .limit(pageSize)
      .offset(offset);

    const records = await dataQuery;

    const data: CreditAcquisitionRecord[] = records.map(record => ({
      id: record.id,
      createdAt: record.createdAt,
      totalCredits: Number(record.totalCredits) || 0,
      notes: record.notes || undefined,
    }));

    return {
      status: 'success',
      data: {
        data,
        total,
        page,
        pageSize
      }
    };

  } catch (error) {
    console.error('获取积分获取流水失败:', error);
    return { status: 'error', message: '获取积分获取流水失败' };
  }
}