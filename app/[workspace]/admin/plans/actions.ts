'use server';

import { db } from '@/app/db';
import { redemptionCodes, redemptionCodeUsages, userWorkspace, creditTransactions, workspaces } from '@/app/db/schema';
import { eq, and, gte, sql } from 'drizzle-orm';
import { auth } from '@/auth';
import { isWorkspaceAdmin } from '@/app/utils/workspace';

export interface RedemptionResult {
  status: 'success' | 'error';
  message: string;
  creditAmount?: number;
}

/**
 * 使用兑换码
 */
export async function redeemCode(workspaceId: string, code: string): Promise<RedemptionResult> {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return { status: 'error', message: '请先登录' };
    }

    // 验证用户是否为工作空间管理员或创建者
    const adminCheck = await isWorkspaceAdmin(workspaceId, session.user.id);
    if (!adminCheck) {
      return { status: 'error', message: '权限不足，只有管理员或创建者可以使用兑换码' };
    }

    // 规范化兑换码（转换为大写）
    const normalizedCode = code.toUpperCase().trim();

    if (!/^[A-Z0-9]{8}$/.test(normalizedCode)) {
      return { status: 'error', message: '兑换码格式不正确' };
    }

    // 使用事务确保数据一致性
    const result = await db.transaction(async (tx) => {
      // 查找兑换码
      const redemptionCode = await tx.query.redemptionCodes.findFirst({
        where: and(
          eq(redemptionCodes.code, normalizedCode),
          eq(redemptionCodes.isActive, true),
          gte(redemptionCodes.expiresAt, new Date()) // 未过期
        )
      });

      if (!redemptionCode) {
        throw new Error('兑换码不存在或已过期');
      }

      // 检查使用次数限制
      if (redemptionCode.usedCount >= redemptionCode.usageLimit) {
        throw new Error('兑换码已达到使用次数限制');
      }

      // 检查该用户在该工作空间是否已经使用过此兑换码
      const existingUsage = await tx.query.redemptionCodeUsages.findFirst({
        where: and(
          eq(redemptionCodeUsages.userId, session.user.id),
          eq(redemptionCodeUsages.workspaceId, workspaceId),
          eq(redemptionCodeUsages.redemptionCodeId, redemptionCode.id)
        )
      });

      if (existingUsage) {
        throw new Error('该兑换码已被使用');
      }

      // 获取工作空间的当前积分余额
      const currentWorkspace = await tx.query.workspaces.findFirst({
        where: eq(workspaces.id, workspaceId),
        columns: {
          creditBalance: true,
        }
      });

      if (!currentWorkspace) {
        throw new Error('工作空间不存在');
      }

      const currentBalance = Number(currentWorkspace.creditBalance) || 0;
      const creditAmount = Number(redemptionCode.creditAmount);
      const newBalance = currentBalance + creditAmount;

      // 更新工作空间积分余额
      await tx.update(workspaces)
        .set({ 
          creditBalance: newBalance,
          updatedAt: new Date()
        })
        .where(eq(workspaces.id, workspaceId));

      // 记录兑换码使用记录
      await tx.insert(redemptionCodeUsages).values({
        userId: session.user.id,
        redemptionCodeId: redemptionCode.id,
        code: normalizedCode,
        workspaceId: workspaceId,
        creditAmount: creditAmount,
        usedAt: new Date()
      });

      // 更新兑换码使用次数
      await tx.update(redemptionCodes)
        .set({ 
          usedCount: redemptionCode.usedCount + 1,
          updatedAt: new Date()
        })
        .where(eq(redemptionCodes.id, redemptionCode.id));

      // 记录积分获取交易记录
      await tx.insert(creditTransactions).values({
        workspaceId: workspaceId,
        userId: session.user.id,
        transactionType: 'redemption_code',
        totalCredits: creditAmount,
        balanceAfter: newBalance,
        relatedEntityId: redemptionCode.id,
        relatedEntityType: 'redemption_code',
        notes: `兑换码：${normalizedCode}`
      });

      return { creditAmount };
    });

    return {
      status: 'success',
      message: `兑换成功！获得 ${result.creditAmount.toLocaleString()} 积分`,
      creditAmount: result.creditAmount
    };

  } catch (error) {
    console.error('兑换码使用失败:', error);
    const errorMessage = error instanceof Error ? error.message : '兑换失败，请重试';
    return { status: 'error', message: errorMessage };
  }
}

/**
 * 生成兑换码（仅供后台管理使用，这里提供函数但不暴露接口）
 */
export async function generateRedemptionCode(): Promise<string> {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  for (let i = 0; i < 8; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}