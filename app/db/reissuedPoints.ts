import 'dotenv/config';
import { workspaces, userWorkspace, creditTransactions } from '@/app/db/schema';
import { db } from './index';
import { eq, and } from 'drizzle-orm';

/**
 * 为历史用户补发工作空间创建积分的迁移脚本
 * 
 * 功能说明：
 * 1. 查找所有workspace
 * 2. 检查这些workspace是否已有创建相关的积分交易记录
 * 3. 对于没有记录的workspace，补发500000积分并记录交易
 * 4. 具备幂等性：已发放过的不会重复发放
 */
export async function reissueWorkspaceCreationCredits() {
  try {
    console.log('开始执行工作空间创建积分补发迁移...');
    
    // 查询所有工作空间
    const allWorkspaces = await db.select().from(workspaces);
    console.log(`找到 ${allWorkspaces.length} 个工作空间需要检查`);
    
    if (allWorkspaces.length === 0) {
      console.log('没有找到工作空间，跳过迁移');
      return;
    }

    let creditsReissued = 0;
    let workspacesProcessed = 0;
    let workspacesSkipped = 0;

    // 使用事务处理每个工作空间
    await db.transaction(async (tx) => {
      for (const workspace of allWorkspaces) {
        const workspaceId = workspace.id;
        const ownerId = workspace.owner;
        
        console.log(`处理工作空间: ${workspaceId} (Owner: ${ownerId})`);

        if (!ownerId) {
          console.log(`  ⚠️  工作空间 ${workspaceId} 没有owner，跳过`);
          workspacesSkipped++;
          continue;
        }

        // 检查是否已存在 workspace_creation 类型的积分交易记录
        const existingTransaction = await tx.select()
          .from(creditTransactions)
          .where(
            and(
              eq(creditTransactions.workspaceId, workspaceId),
              eq(creditTransactions.userId, ownerId),
              eq(creditTransactions.relatedEntityType, 'workspace_creation'),
              eq(creditTransactions.transactionType, 'gift')
            )
          );

        // 如果已存在创建积分记录，则跳过
        if (existingTransaction.length > 0) {
          console.log(`  ⏭️  工作空间 ${workspaceId} 已存在创建积分记录，跳过`);
          workspacesSkipped++;
          continue;
        }

        // 查找该工作空间的owner用户记录（用于验证）
        const ownerUserWorkspace = await tx.select()
          .from(userWorkspace)
          .where(
            and(
              eq(userWorkspace.workspaceId, workspaceId),
              eq(userWorkspace.userId, ownerId),
              eq(userWorkspace.role, 'owner')
            )
          );

        if (ownerUserWorkspace.length === 0) {
          console.log(`  ⚠️  未找到工作空间 ${workspaceId} 的owner用户记录，跳过`);
          workspacesSkipped++;
          continue;
        }

        // 获取工作空间当前积分余额
        const currentWorkspace = await tx.select()
          .from(workspaces)
          .where(eq(workspaces.id, workspaceId));

        if (currentWorkspace.length === 0) {
          console.log(`  ⚠️  工作空间 ${workspaceId} 不存在，跳过`);
          workspacesSkipped++;
          continue;
        }

        const currentBalance = Number(currentWorkspace[0].creditBalance || 0);
        const creditsToAdd = 500000;
        const newBalance = currentBalance + creditsToAdd;

        // 更新工作空间的积分余额
        await tx.update(workspaces)
          .set({ 
            creditBalance: newBalance,
            updatedAt: new Date()
          })
          .where(eq(workspaces.id, workspaceId));

        // 插入积分交易记录
        await tx.insert(creditTransactions).values({
          workspaceId: workspaceId,
          userId: ownerId,
          transactionType: 'gift',
          totalCredits: creditsToAdd,
          balanceAfter: newBalance,
          relatedEntityId: workspaceId,
          relatedEntityType: 'workspace_creation',
          notes: '注册赠送积分补发'
        });

        creditsReissued += creditsToAdd;
        workspacesProcessed++;
        console.log(`  ✅ 为工作空间 ${workspaceId} 补发了 ${creditsToAdd} 积分 (余额: ${currentBalance} → ${newBalance})`);
      }
    });

    console.log(`✅ 积分补发迁移完成！`);
    console.log(`📊 统计信息:`);
    console.log(`   - 检查工作空间数: ${allWorkspaces.length}`);
    console.log(`   - 补发积分工作空间数: ${workspacesProcessed}`);
    console.log(`   - 跳过工作空间数: ${workspacesSkipped}`);
    console.log(`   - 总补发积分数: ${creditsReissued.toLocaleString()}`);

  } catch (error) {
    console.error('❌ 积分补发迁移失败:', error);
    throw error;
  }
}

// 执行迁移脚本
reissueWorkspaceCreationCredits().then(() => {
  console.log("积分补发迁移执行成功");
  process.exit(0);
}).catch((error) => {
  console.error("积分补发迁移执行失败:", error);
  process.exit(1);
});